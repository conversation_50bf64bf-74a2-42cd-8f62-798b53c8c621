<?php
// Enable error reporting for debugging (remove in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Error logging function
function logError($message) {
    $logFile = 'application_logs.log';
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] $message" . PHP_EOL;
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
}

// Function to send email using Mailgun API
function sendMailgunEmail($formData) {
    // Mailgun API credentials - replace with your actual values
    $mailgunDomain = "sandbox78984fc611a942b7ba8312015bc82e93.mailgun.org"; // Your Mailgun domain
    $mailgunApiKey = "**************************************************"; // Your Mailgun API key
    
    // Set up recipients
    $to = "<EMAIL>, <EMAIL>";
    
    // Set email subject
    $subject = "New Employment Application - " . $formData['first_name'] . " " . $formData['last_name'];
    
    // Create HTML email content with McKiness branding
    $htmlContent = '
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>New Employment Application - McKiness Excavating</title>
        <style type="text/css">
            /* Reset and base styles */
            body, html {
                margin: 0;
                padding: 0;
                font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
                line-height: 1.6;
                color: #333333;
                background-color: #f9f9f9;
            }

            /* Container */
            .email-container {
                max-width: 800px;
                margin: 0 auto;
                background-color: #ffffff;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            }

            /* Header */
            .email-header {
                background: linear-gradient(135deg, #2f6156 0%, #254a42 100%);
                color: #ffffff;
                padding: 30px 40px;
                text-align: center;
                border-bottom: 4px solid #f9d016;
            }

            .company-logo {
                font-size: 28px;
                font-weight: bold;
                color: #f9d016;
                margin-bottom: 10px;
                text-transform: uppercase;
                letter-spacing: 2px;
            }

            .email-title {
                font-size: 24px;
                margin: 0;
                font-weight: 600;
                color: #ffffff;
            }

            .submission-date {
                font-size: 14px;
                color: #e0e0e0;
                margin-top: 10px;
                font-style: italic;
            }

            /* Content sections */
            .email-content {
                padding: 0;
            }

            .section {
                padding: 30px 40px;
                border-bottom: 1px solid #e8e8e8;
            }

            .section:last-child {
                border-bottom: none;
            }

            .section-title {
                color: #2f6156;
                font-size: 20px;
                font-weight: 600;
                margin: 0 0 20px 0;
                padding-bottom: 10px;
                border-bottom: 2px solid #f9d016;
                display: inline-block;
                min-width: 200px;
            }

            /* Field styling */
            .field-row {
                margin-bottom: 15px;
                display: flex;
                flex-wrap: wrap;
                align-items: flex-start;
            }

            .field-label {
                font-weight: 600;
                color: #2f6156;
                min-width: 180px;
                margin-right: 20px;
                margin-bottom: 5px;
            }

            .field-value {
                flex: 1;
                color: #333333;
                background-color: #f8f9fa;
                padding: 8px 12px;
                border-radius: 4px;
                border-left: 3px solid #f9d016;
                min-width: 200px;
            }

            /* Highlight important fields */
            .highlight-field .field-value {
                background-color: #fff8e1;
                border-left-color: #2f6156;
                font-weight: 500;
            }

            /* Subsection styling */
            .subsection {
                margin: 25px 0;
                padding: 20px;
                background-color: #fafafa;
                border-radius: 6px;
                border: 1px solid #e0e0e0;
            }

            .subsection-title {
                color: #2f6156;
                font-size: 16px;
                font-weight: 600;
                margin: 0 0 15px 0;
                padding-bottom: 8px;
                border-bottom: 1px solid #f9d016;
            }

            /* Footer */
            .email-footer {
                background-color: #2f6156;
                color: #ffffff;
                padding: 30px 40px;
                text-align: center;
            }

            .footer-content {
                font-size: 14px;
                line-height: 1.5;
            }

            .footer-logo {
                font-size: 18px;
                font-weight: bold;
                color: #f9d016;
                margin-bottom: 10px;
                text-transform: uppercase;
                letter-spacing: 1px;
            }

            /* Responsive design */
            @media screen and (max-width: 600px) {
                .email-container {
                    margin: 0;
                    box-shadow: none;
                }

                .email-header,
                .section,
                .email-footer {
                    padding: 20px;
                }

                .field-row {
                    flex-direction: column;
                }

                .field-label {
                    min-width: auto;
                    margin-right: 0;
                    margin-bottom: 5px;
                }

                .field-value {
                    min-width: auto;
                    width: 100%;
                }

                .company-logo {
                    font-size: 24px;
                }

                .email-title {
                    font-size: 20px;
                }

                .section-title {
                    font-size: 18px;
                }
            }

            /* Print styles */
            @media print {
                .email-container {
                    box-shadow: none;
                    max-width: none;
                }

                .email-header {
                    background: #2f6156 !important;
                    -webkit-print-color-adjust: exact;
                }
            }
        </style>
    </head>
    <body>
        <div class="email-container">
            <!-- Header -->
            <div class="email-header">
                <div class="company-logo">McKiness Excavating</div>
                <h1 class="email-title">New Employment Application</h1>
                <div class="submission-date">Submitted on ' . date('F j, Y \a\t g:i A') . '</div>
            </div>

            <!-- Content -->
            <div class="email-content">
                <!-- Personal Information Section -->
                <div class="section">
                    <h2 class="section-title">Personal Information</h2>

                    <div class="field-row highlight-field">
                        <div class="field-label">Full Name:</div>
                        <div class="field-value">' .
                            htmlspecialchars($formData['first_name']) . ' ' .
                            (isset($formData['middle_name']) && !empty($formData['middle_name']) ? htmlspecialchars($formData['middle_name']) . ' ' : '') .
                            htmlspecialchars($formData['last_name']) .
                        '</div>
                    </div>

                    <div class="field-row highlight-field">
                        <div class="field-label">Email Address:</div>
                        <div class="field-value">' . htmlspecialchars($formData['email']) . '</div>
                    </div>

                    <div class="field-row">
                        <div class="field-label">Phone Number:</div>
                        <div class="field-value">' . (isset($formData['phone']) && !empty($formData['phone']) ? htmlspecialchars($formData['phone']) : 'Not provided') . '</div>
                    </div>';

    // Add address if provided
    if (isset($formData['address_street']) && !empty($formData['address_street'])) {
        $address = htmlspecialchars($formData['address_street']);
        if (isset($formData['address_city']) && !empty($formData['address_city'])) {
            $address .= ', ' . htmlspecialchars($formData['address_city']);
        }
        if (isset($formData['address_state']) && !empty($formData['address_state'])) {
            $address .= ', ' . htmlspecialchars($formData['address_state']);
        }
        if (isset($formData['address_zip']) && !empty($formData['address_zip'])) {
            $address .= ' ' . htmlspecialchars($formData['address_zip']);
        }

        $htmlContent .= '
                    <div class="field-row">
                        <div class="field-label">Address:</div>
                        <div class="field-value">' . $address . '</div>
                    </div>';
    }

    if (isset($formData['referred_by']) && !empty($formData['referred_by'])) {
        $htmlContent .= '
                    <div class="field-row">
                        <div class="field-label">Referred By:</div>
                        <div class="field-value">' . htmlspecialchars($formData['referred_by']) . '</div>
                    </div>';
    }

    $htmlContent .= '
                </div>

                <!-- Employment Desired Section -->
                <div class="section">
                    <h2 class="section-title">Employment Desired</h2>

                    <div class="field-row highlight-field">
                        <div class="field-label">Position Desired:</div>
                        <div class="field-value">' . (isset($formData['position']) && !empty($formData['position']) ? htmlspecialchars($formData['position']) : 'Not specified') . '</div>
                    </div>

                    <div class="field-row highlight-field">
                        <div class="field-label">Employment Type:</div>
                        <div class="field-value">' . (isset($formData['employment_type']) ? htmlspecialchars($formData['employment_type']) : 'Not specified') . '</div>
                    </div>

                    <div class="field-row highlight-field">
                        <div class="field-label">Desired Start Date:</div>
                        <div class="field-value">' . htmlspecialchars($formData['start_date']) . '</div>
                    </div>

                    <div class="field-row highlight-field">
                        <div class="field-label">Desired Hourly Wage:</div>
                        <div class="field-value">' . htmlspecialchars($formData['wage']) . '</div>
                    </div>

                    <div class="field-row">
                        <div class="field-label">Currently Employed:</div>
                        <div class="field-value">' . (isset($formData['currently_employed']) ? htmlspecialchars($formData['currently_employed']) : 'Not specified') . '</div>
                    </div>

                    <div class="field-row">
                        <div class="field-label">May we inquire with present employer:</div>
                        <div class="field-value">' . (isset($formData['inquire_employer']) ? htmlspecialchars($formData['inquire_employer']) : 'Not specified') . '</div>
                    </div>

                    <div class="field-row">
                        <div class="field-label">Previously applied to company:</div>
                        <div class="field-value">' . (isset($formData['applied_before']) ? htmlspecialchars($formData['applied_before']) : 'Not specified') . '</div>
                    </div>';

    if (isset($formData['applied_when']) && !empty($formData['applied_when'])) {
        $htmlContent .= '
                    <div class="field-row">
                        <div class="field-label">Previous application date:</div>
                        <div class="field-value">' . htmlspecialchars($formData['applied_when']) . '</div>
                    </div>';
    }

    $htmlContent .= '
                </div>

                <!-- Education Section -->
                <div class="section">
                    <h2 class="section-title">Education</h2>

                    <div class="field-row">
                        <div class="field-label">High School:</div>
                        <div class="field-value">' . htmlspecialchars($formData['high_school']) . '</div>
                    </div>';

    if (isset($formData['college']) && !empty($formData['college'])) {
        $htmlContent .= '
                    <div class="field-row">
                        <div class="field-label">College:</div>
                        <div class="field-value">' . htmlspecialchars($formData['college']) . '</div>
                    </div>';
    }

    if (isset($formData['trade_school']) && !empty($formData['trade_school'])) {
        $htmlContent .= '
                    <div class="field-row">
                        <div class="field-label">Trade/Business School:</div>
                        <div class="field-value">' . htmlspecialchars($formData['trade_school']) . '</div>
                    </div>';
    }

    if (isset($formData['special_study']) && !empty($formData['special_study'])) {
        $htmlContent .= '
                    <div class="field-row">
                        <div class="field-label">Special Study/Research:</div>
                        <div class="field-value">' . htmlspecialchars($formData['special_study']) . '</div>
                    </div>';
    }

    $htmlContent .= '
                    <div class="field-row">
                        <div class="field-label">U.S. Military/Naval Service:</div>
                        <div class="field-value">' . (isset($formData['military_service']) ? htmlspecialchars($formData['military_service']) : 'Not specified') . '</div>
                    </div>

                    <div class="field-row">
                        <div class="field-label">National Guard/Reserve:</div>
                        <div class="field-value">' . (isset($formData['national_guard']) ? htmlspecialchars($formData['national_guard']) : 'Not specified') . '</div>
                    </div>';

    if (isset($formData['activities']) && !empty($formData['activities'])) {
        $htmlContent .= '
                    <div class="field-row">
                        <div class="field-label">Activities (civic, athletic, etc.):</div>
                        <div class="field-value">' . nl2br(htmlspecialchars($formData['activities'])) . '</div>
                    </div>';
    }

    $htmlContent .= '
                    <div class="field-row">
                        <div class="field-label">Criminal Conviction Information:</div>
                        <div class="field-value">' . nl2br(htmlspecialchars($formData['criminal_conviction'])) . '</div>
                    </div>
                </div>

                <!-- Employment History Section -->
                <div class="section">
                    <h2 class="section-title">Employment History</h2>

                    <div class="subsection">
                        <h3 class="subsection-title">Employer #1 (Most Recent)</h3>

                        <div class="field-row">
                            <div class="field-label">Company Name & Address:</div>
                            <div class="field-value">' . nl2br(htmlspecialchars($formData['employer1_name_address'])) . '</div>
                        </div>

                        <div class="field-row">
                            <div class="field-label">Employment Dates:</div>
                            <div class="field-value">' . htmlspecialchars($formData['employer1_dates']) . '</div>
                        </div>

                        <div class="field-row">
                            <div class="field-label">Position & Salary:</div>
                            <div class="field-value">' . nl2br(htmlspecialchars($formData['employer1_position_salary'])) . '</div>
                        </div>

                        <div class="field-row">
                            <div class="field-label">Reason for Leaving:</div>
                            <div class="field-value">' . nl2br(htmlspecialchars($formData['employer1_reason'])) . '</div>
                        </div>
                    </div>';

    // Add other employers if provided
    if (isset($formData['employer2_name_address']) && !empty($formData['employer2_name_address'])) {
        $htmlContent .= '
                    <div class="subsection">
                        <h3 class="subsection-title">Employer #2</h3>

                        <div class="field-row">
                            <div class="field-label">Company Name & Address:</div>
                            <div class="field-value">' . nl2br(htmlspecialchars($formData['employer2_name_address'])) . '</div>
                        </div>

                        <div class="field-row">
                            <div class="field-label">Employment Dates:</div>
                            <div class="field-value">' . (isset($formData['employer2_dates']) && !empty($formData['employer2_dates']) ? htmlspecialchars($formData['employer2_dates']) : 'Not provided') . '</div>
                        </div>

                        <div class="field-row">
                            <div class="field-label">Position & Salary:</div>
                            <div class="field-value">' . (isset($formData['employer2_position_salary']) && !empty($formData['employer2_position_salary']) ? nl2br(htmlspecialchars($formData['employer2_position_salary'])) : 'Not provided') . '</div>
                        </div>

                        <div class="field-row">
                            <div class="field-label">Reason for Leaving:</div>
                            <div class="field-value">' . (isset($formData['employer2_reason']) && !empty($formData['employer2_reason']) ? nl2br(htmlspecialchars($formData['employer2_reason'])) : 'Not provided') . '</div>
                        </div>
                    </div>';
    }

    $htmlContent .= '
                </div>

                <!-- References Section -->
                <div class="section">
                    <h2 class="section-title">References</h2>

                    <div class="field-row">
                        <div class="field-label">Reference #1:</div>
                        <div class="field-value">' . nl2br(htmlspecialchars($formData['reference1'])) . '</div>
                    </div>

                    <div class="field-row">
                        <div class="field-label">Reference #2:</div>
                        <div class="field-value">' . nl2br(htmlspecialchars($formData['reference2'])) . '</div>
                    </div>
                </div>

                <!-- Experience Section -->
                <div class="section">';

    // Check if any experience fields are provided
    $hasExperience = false;
    $experienceFields = ['carpenter_experience', 'laborer_experience', 'operator_experience', 'truck_driver_experience'];
    foreach ($experienceFields as $field) {
        if (isset($formData[$field]) && !empty($formData[$field])) {
            $hasExperience = true;
            break;
        }
    }

    if ($hasExperience) {
        $htmlContent .= '
                    <h2 class="section-title">Experience</h2>';

        if (isset($formData['carpenter_experience']) && !empty($formData['carpenter_experience'])) {
            $htmlContent .= '
                    <div class="field-row">
                        <div class="field-label">Carpenter Experience:</div>
                        <div class="field-value">' . nl2br(htmlspecialchars($formData['carpenter_experience'])) . '</div>
                    </div>';
        }

        if (isset($formData['laborer_experience']) && !empty($formData['laborer_experience'])) {
            $htmlContent .= '
                    <div class="field-row">
                        <div class="field-label">Laborer Experience:</div>
                        <div class="field-value">' . nl2br(htmlspecialchars($formData['laborer_experience'])) . '</div>
                    </div>';
        }

        if (isset($formData['operator_experience']) && !empty($formData['operator_experience'])) {
            $htmlContent .= '
                    <div class="field-row">
                        <div class="field-label">Operator Experience:</div>
                        <div class="field-value">' . nl2br(htmlspecialchars($formData['operator_experience'])) . '</div>
                    </div>';
        }

        if (isset($formData['truck_driver_experience']) && !empty($formData['truck_driver_experience'])) {
            $htmlContent .= '
                    <div class="field-row">
                        <div class="field-label">Truck Driver Experience:</div>
                        <div class="field-value">' . nl2br(htmlspecialchars($formData['truck_driver_experience'])) . '</div>
                    </div>';
        }
    }

    $htmlContent .= '
                </div>

                <!-- Driver Information Section -->
                <div class="section">
                    <h2 class="section-title">Driver Information</h2>

                    <div class="field-row">
                        <div class="field-label">Iowa Driver\'s License:</div>
                        <div class="field-value">' . (isset($formData['iowa_license']) ? htmlspecialchars($formData['iowa_license']) : 'Not specified') . '</div>
                    </div>

                    <div class="field-row">
                        <div class="field-label">License Number:</div>
                        <div class="field-value">' . htmlspecialchars($formData['license_number']) . '</div>
                    </div>

                    <div class="field-row">
                        <div class="field-label">CDL License:</div>
                        <div class="field-value">' . (isset($formData['cdl_license']) ? htmlspecialchars($formData['cdl_license']) : 'Not specified') . '</div>
                    </div>';

    if (isset($formData['cdl_class']) && !empty($formData['cdl_class'])) {
        $htmlContent .= '
                    <div class="field-row">
                        <div class="field-label">CDL Class:</div>
                        <div class="field-value">' . htmlspecialchars($formData['cdl_class']) . '</div>
                    </div>';
    }

    $htmlContent .= '
                    <div class="field-row">
                        <div class="field-label">On SR-22 Insurance:</div>
                        <div class="field-value">' . (isset($formData['on_sr22']) ? htmlspecialchars($formData['on_sr22']) : 'Not specified') . '</div>
                    </div>

                    <div class="field-row">
                        <div class="field-label">Past SR-22:</div>
                        <div class="field-value">' . (isset($formData['past_sr22']) ? htmlspecialchars($formData['past_sr22']) : 'Not specified') . '</div>
                    </div>';

    if (isset($formData['sr22_when']) && !empty($formData['sr22_when'])) {
        $htmlContent .= '
                    <div class="field-row">
                        <div class="field-label">SR-22 When:</div>
                        <div class="field-value">' . htmlspecialchars($formData['sr22_when']) . '</div>
                    </div>';
    }

    $htmlContent .= '
                </div>';
    
    // Additional Information Section
    $htmlContent .= '

                <!-- Additional Information Section -->
                <div class="section">
                    <h2 class="section-title">Additional Information</h2>';

    if (isset($formData['supervisor_experience']) && !empty($formData['supervisor_experience'])) {
        $htmlContent .= '
                    <div class="field-row">
                        <div class="field-label">Supervisor Experience:</div>
                        <div class="field-value">' . nl2br(htmlspecialchars($formData['supervisor_experience'])) . '</div>
                    </div>';
    }

    if (isset($formData['other_responsibilities']) && !empty($formData['other_responsibilities'])) {
        $htmlContent .= '
                    <div class="field-row">
                        <div class="field-label">Other Responsibilities:</div>
                        <div class="field-value">' . nl2br(htmlspecialchars($formData['other_responsibilities'])) . '</div>
                    </div>';
    }

    $htmlContent .= '
                    <div class="field-row">
                        <div class="field-label">U.S. Citizen:</div>
                        <div class="field-value">' . (isset($formData['us_citizen']) ? htmlspecialchars($formData['us_citizen']) : 'Not specified') . '</div>
                    </div>

                    <div class="field-row">
                        <div class="field-label">Years in State:</div>
                        <div class="field-value">' . htmlspecialchars($formData['years_in_state']) . '</div>
                    </div>

                    <div class="field-row">
                        <div class="field-label">Read Job Descriptions:</div>
                        <div class="field-value">' . (isset($formData['read_descriptions']) ? htmlspecialchars($formData['read_descriptions']) : 'Not specified') . '</div>
                    </div>

                    <div class="field-row">
                        <div class="field-label">Willing to take drug test:</div>
                        <div class="field-value">' . (isset($formData['drug_test']) ? htmlspecialchars($formData['drug_test']) : 'Not specified') . '</div>
                    </div>

                    <div class="field-row">
                        <div class="field-label">Physical/Mental Limitations:</div>
                        <div class="field-value">' . (isset($formData['limitations']) ? htmlspecialchars($formData['limitations']) : 'Not specified') . '</div>
                    </div>';

    if (isset($formData['limitations']) && $formData['limitations'] == 'Yes' && isset($formData['limitations_explanation']) && !empty($formData['limitations_explanation'])) {
        $htmlContent .= '
                    <div class="field-row">
                        <div class="field-label">Limitations Explanation:</div>
                        <div class="field-value">' . nl2br(htmlspecialchars($formData['limitations_explanation'])) . '</div>
                    </div>';
    }

    $htmlContent .= '
                    <div class="field-row">
                        <div class="field-label">Willing to take evaluation:</div>
                        <div class="field-value">' . (isset($formData['willing_to_evaluate']) ? htmlspecialchars($formData['willing_to_evaluate']) : 'Not specified') . '</div>
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <div class="email-footer">
                <div class="footer-logo">McKiness Excavating</div>
                <div class="footer-content">
                    <p>This employment application was submitted through our website.</p>
                    <p>Please review the information and contact the applicant if their qualifications match our current needs.</p>
                    <p style="margin-top: 20px; font-size: 12px; color: #cccccc;">
                        This email was automatically generated by the McKiness Excavating employment application system.
                    </p>
                </div>
            </div>
        </div>
    </body>
    </html>';
    
    // Create plain text version
    $plainText = "New Employment Application\n\n";
    $plainText .= "Submitted on: " . date('F j, Y, g:i a') . "\n\n";
    $plainText .= "PERSONAL INFORMATION\n";
    $plainText .= "Name: " . $formData['first_name'] . " " . $formData['last_name'] . "\n";
    $plainText .= "Email: " . $formData['email'] . "\n";
    $plainText .= "Phone: " . (isset($formData['phone']) ? $formData['phone'] : 'Not provided') . "\n\n";
    
    // Prepare the Mailgun API request
    $url = "https://api.mailgun.net/v3/$mailgunDomain/messages";
    $params = array(
        'from'    => 'McKiness Application System <<EMAIL>>',
        'to'      => $to,
        'subject' => $subject,
        'html'    => $htmlContent,
        'text'    => $plainText
    );
    
    // Add BCC
    $params['bcc'] = '<EMAIL>';
    
    // Initialize cURL
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
    curl_setopt($ch, CURLOPT_USERPWD, "api:$mailgunApiKey");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
    
    // Execute the cURL request
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    // Log the response
    logError("Mailgun API response: HTTP $httpCode - $response");
    
    // Check if the request was successful
    if ($httpCode == 200) {
        return ['success' => true, 'message' => 'Email sent successfully'];
    } else {
        return ['success' => false, 'message' => "Failed to send email. HTTP code: $httpCode"];
    }
}

// Save form data to a backup file
function saveFormData($formData) {
    $backupDir = 'form_submissions';
    
    // Create directory if it doesn't exist
    if (!file_exists($backupDir)) {
        mkdir($backupDir, 0755, true);
    }
    
    // Generate a unique filename
    $filename = $backupDir . '/' . date('Y-m-d_H-i-s') . '_' . 
                preg_replace('/[^a-z0-9]/i', '_', $formData['last_name']) . '.json';
    
    // Save the data as JSON
    return file_put_contents($filename, json_encode($formData, JSON_PRETTY_PRINT));
}

// Main processing logic
try {
    // Log the form submission
    logError("Form submission received");
    
    // Collect form data
    $formData = $_POST;
    
    // Enhanced validation for all required fields
    $requiredFields = [
        'email' => 'Email',
        'first_name' => 'First Name',
        'last_name' => 'Last Name',
        'start_date' => 'Desired Start Date',
        'wage' => 'Desired Hourly Wage',
        'employment_type' => 'Employment Type',
        'currently_employed' => 'Currently Employed',
        'inquire_employer' => 'May we inquire with present employer',
        'applied_before' => 'Previously applied to company',
        'high_school' => 'High School Information',
        'military_service' => 'Military Service',
        'national_guard' => 'National Guard/Reserve',
        'criminal_conviction' => 'Criminal Conviction Information',
        'employer1_name_address' => 'Employer #1 Name and Address',
        'employer1_dates' => 'Employer #1 Date Range',
        'employer1_position_salary' => 'Employer #1 Position and Salary',
        'employer1_reason' => 'Employer #1 Reason for Leaving',
        'reference1' => 'Reference #1',
        'reference2' => 'Reference #2',
        'iowa_license' => 'Iowa Driver\'s License',
        'license_number' => 'Driver\'s License Number',
        'cdl_license' => 'CDL License',
        'on_sr22' => 'SR-22 Insurance Status',
        'past_sr22' => 'Past SR-22 Status',
        'us_citizen' => 'U.S. Citizenship',
        'years_in_state' => 'Years in State',
        'read_descriptions' => 'Read Job Descriptions',
        'drug_test' => 'Willing to take drug test',
        'limitations' => 'Physical/Mental Limitations',
        'willing_to_evaluate' => 'Willing to take evaluation'
    ];

    $missingFields = [];
    foreach ($requiredFields as $field => $label) {
        if (empty($formData[$field]) || trim($formData[$field]) === '') {
            $missingFields[] = $label;
        }
    }

    if (!empty($missingFields)) {
        $errorMessage = "Please fill in all required fields: " . implode(', ', $missingFields);
        logError("Missing required fields: " . implode(', ', $missingFields));
        header("Location: application.html?error=" . urlencode($errorMessage));
        exit;
    }

    // Email validation
    if (!filter_var($formData['email'], FILTER_VALIDATE_EMAIL)) {
        logError("Invalid email format: " . $formData['email']);
        header("Location: application.html?error=" . urlencode("Please enter a valid email address."));
        exit;
    }
    
    // Log key form fields
    logError("Processing application for: " . $formData['first_name'] . " " . $formData['last_name'] . " (" . $formData['email'] . ")");
    
    // Save form data as backup
    if (saveFormData($formData)) {
        logError("Form data saved to backup file");
    } else {
        logError("Failed to save form data to backup file");
    }
    
    // Send email using Mailgun
    $emailResult = sendMailgunEmail($formData);
    
    if ($emailResult['success']) {
        // Email sent successfully
        logError("Email sent successfully. Redirecting to thank you page.");
        header("Location: thankyou.html?success=1");
        exit;
    } else {
        // Email failed
        $errorMessage = "We're sorry, but there was a problem sending your application. " . 
                        "Please try again or contact us <NAME_EMAIL>.";
        logError("Email sending failed: " . $emailResult['message']);
        header("Location: application.html?error=" . urlencode($errorMessage));
        exit;
    }
} catch (Exception $e) {
    // Catch any unexpected errors
    $errorMessage = "An unexpected error occurred. Please try again or contact us directly.";
    logError("Exception: " . $e->getMessage());
    header("Location: application.html?error=" . urlencode($errorMessage));
    exit;
}
?>