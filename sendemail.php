<?php
// Include the email template generator
require_once('email-template.php');

// Server-side validation function
function validateApplicationForm($data) {
    $errors = [];

    // Required fields validation
    $requiredFields = [
        'email' => 'Email',
        'first_name' => 'First Name',
        'last_name' => 'Last Name',
        'start_date' => 'Desired Start Date',
        'wage' => 'Desired Hourly Wage',
        'employment_type' => 'Employment Type',
        'currently_employed' => 'Currently Employed',
        'inquire_employer' => 'May we inquire with present employer',
        'applied_before' => 'Previously applied to company',
        'high_school' => 'High School Information',
        'military_service' => 'Military Service',
        'national_guard' => 'National Guard/Reserve',
        'criminal_conviction' => 'Criminal Conviction Information',
        'employer1_name_address' => 'Employer #1 Name and Address',
        'employer1_dates' => 'Employer #1 Date Range',
        'employer1_position_salary' => 'Employer #1 Position and Salary',
        'employer1_reason' => 'Employer #1 Reason for Leaving',
        'reference1' => 'Reference #1',
        'reference2' => 'Reference #2',
        'iowa_license' => 'Iowa Driver\'s License',
        'license_number' => 'Driver\'s License Number',
        'cdl_license' => 'CDL License',
        'on_sr22' => 'SR-22 Insurance Status',
        'past_sr22' => 'Past SR-22 Status',
        'us_citizen' => 'U.S. Citizenship',
        'years_in_state' => 'Years in State',
        'read_descriptions' => 'Read Job Descriptions',
        'drug_test' => 'Willing to take drug test',
        'limitations' => 'Physical/Mental Limitations',
        'willing_to_evaluate' => 'Willing to take evaluation'
    ];

    foreach ($requiredFields as $field => $label) {
        if (empty($data[$field]) || trim($data[$field]) === '') {
            $errors[] = "$label is required.";
        }
    }

    // Email validation
    if (!empty($data['email']) && !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
        $errors[] = "Please enter a valid email address.";
    }

    // Date validation
    if (!empty($data['start_date'])) {
        $date = DateTime::createFromFormat('Y-m-d', $data['start_date']);
        if (!$date || $date->format('Y-m-d') !== $data['start_date']) {
            $errors[] = "Please enter a valid start date.";
        }
    }

    return $errors;
}

// Validate the form data
$validationErrors = validateApplicationForm($_POST);

if (!empty($validationErrors)) {
    // If there are validation errors, redirect back with error message
    $errorMessage = implode(' ', $validationErrors);
    header("Location: application.html?error=" . urlencode($errorMessage));
    exit;
}

// Set recipient emails
$to = "<EMAIL>, <EMAIL>";

// Set email subject
$subject = "New Employment Application";

// Collect form data
$formData = $_POST;

// Generate HTML email content
$htmlMessage = generateEmailTemplate($formData);

// Create a plain text version as fallback
$plainTextMessage = "";
foreach ($_POST as $key => $value) {
    $plainTextMessage .= ucfirst($key) . ": " . htmlspecialchars($value) . "\n";
}

// Set up email headers for HTML email with plain text alternative
$boundary = md5(time());
$headers = "From: <EMAIL>\r\n";
$headers .= "MIME-Version: 1.0\r\n";
$headers .= "Bcc: <EMAIL>\r\n";
$headers .= "Content-Type: multipart/alternative; boundary=\"" . $boundary . "\"\r\n";

// Construct the email body with both plain text and HTML versions
$message = "--" . $boundary . "\r\n";
$message .= "Content-Type: text/plain; charset=UTF-8\r\n";
$message .= "Content-Transfer-Encoding: 7bit\r\n\r\n";
$message .= $plainTextMessage . "\r\n\r\n";

$message .= "--" . $boundary . "\r\n";
$message .= "Content-Type: text/html; charset=UTF-8\r\n";
$message .= "Content-Transfer-Encoding: 7bit\r\n\r\n";
$message .= $htmlMessage . "\r\n\r\n";

$message .= "--" . $boundary . "--";

// Send the email
mail($to, $subject, $message, $headers);

// Redirect to thank you page
header("Location: thankyou.html");
exit;
?>
