<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Form Submission - McKiness Application</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; line-height: 1.6; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea, select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #2f6156; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #254a42; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 5px; margin: 20px 0; }
    </style>
</head>
<body>
    <h1>Test Form Submission</h1>
    
    <div class="warning">
        <strong>Test Purpose:</strong> This form contains minimal test data to verify the Mailgun email functionality. 
        Remove this file from production servers.
    </div>
    
    <div class="info">
        <strong>Note:</strong> This form will submit to the same handler as the main application form, 
        but with pre-filled test data to ensure all required fields are present.
    </div>
    
    <form action="mailgun-handler.php" method="POST">
        <h2>Test Data (Pre-filled)</h2>
        
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" name="email" value="<EMAIL>" required>
        </div>
        
        <div class="form-group">
            <label for="first_name">First Name:</label>
            <input type="text" id="first_name" name="first_name" value="Test" required>
        </div>
        
        <div class="form-group">
            <label for="last_name">Last Name:</label>
            <input type="text" id="last_name" name="last_name" value="User" required>
        </div>
        
        <!-- Hidden fields with test data for all required fields -->
        <input type="hidden" name="start_date" value="2024-02-01">
        <input type="hidden" name="wage" value="$15.00">
        <input type="hidden" name="employment_type" value="Full Time">
        <input type="hidden" name="currently_employed" value="No">
        <input type="hidden" name="inquire_employer" value="N/A">
        <input type="hidden" name="applied_before" value="No">
        <input type="hidden" name="high_school" value="Test High School, Test City, 2020, General Studies">
        <input type="hidden" name="military_service" value="No">
        <input type="hidden" name="national_guard" value="No">
        <input type="hidden" name="criminal_conviction" value="No">
        <input type="hidden" name="employer1_name_address" value="Previous Employer Inc, 123 Main St, Test City, IA">
        <input type="hidden" name="employer1_dates" value="2020-2023">
        <input type="hidden" name="employer1_position_salary" value="General Worker - $12/hour">
        <input type="hidden" name="employer1_reason" value="Career change">
        <input type="hidden" name="reference1" value="John Doe, 555-1234, Test City, IA, Construction, 3 years">
        <input type="hidden" name="reference2" value="Jane Smith, 555-5678, Test City, IA, Manufacturing, 2 years">
        <input type="hidden" name="iowa_license" value="Yes">
        <input type="hidden" name="license_number" value="TEST123456789">
        <input type="hidden" name="cdl_license" value="No">
        <input type="hidden" name="on_sr22" value="No">
        <input type="hidden" name="past_sr22" value="No">
        <input type="hidden" name="us_citizen" value="Yes">
        <input type="hidden" name="years_in_state" value="5">
        <input type="hidden" name="read_descriptions" value="Yes">
        <input type="hidden" name="drug_test" value="Yes">
        <input type="hidden" name="limitations" value="No">
        <input type="hidden" name="willing_to_evaluate" value="Yes">
        
        <!-- Optional fields with some test data -->
        <input type="hidden" name="phone" value="************">
        <input type="hidden" name="position" value="General Laborer">
        <input type="hidden" name="address_street" value="123 Test Street">
        <input type="hidden" name="address_city" value="Test City">
        <input type="hidden" name="address_state" value="IA">
        <input type="hidden" name="address_zip" value="12345">
        
        <button type="submit">Submit Test Application</button>
    </form>
    
    <h2>What This Test Does</h2>
    <ul>
        <li>Submits a complete application with all required fields</li>
        <li>Tests the Mailgun email sending functionality</li>
        <li>Verifies form validation and error handling</li>
        <li>Creates log entries for debugging</li>
        <li>Saves backup form data</li>
    </ul>
    
    <h2>Expected Results</h2>
    <ul>
        <li><strong>Success:</strong> Redirects to thankyou.html?success=1</li>
        <li><strong>Failure:</strong> Redirects back with error message</li>
        <li><strong>Email:</strong> Should be received at configured recipients</li>
        <li><strong>Logs:</strong> Check application_logs.log for details</li>
    </ul>
    
    <h2>Troubleshooting</h2>
    <ul>
        <li><strong>Validation errors:</strong> Check that all required fields are present</li>
        <li><strong>Mailgun errors:</strong> Run the Mailgun test first</li>
        <li><strong>No redirect:</strong> Check for PHP errors in server logs</li>
        <li><strong>No email:</strong> Check Mailgun dashboard for delivery status</li>
    </ul>
    
    <p><a href="test-mailgun.php">← Back to Mailgun Test</a> | <a href="application.html">Full Application Form →</a></p>
    
</body>
</html>
