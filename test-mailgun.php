<?php
/**
 * Mailgun Configuration Test Script
 * Use this to test your Mailgun setup and diagnose issues
 */

// Include the main handler for logging function
require_once('mailgun-handler.php');

// Test Mailgun configuration
function testMailgunConfig() {
    // These should match your mailgun-handler.php settings
    $mailgunDomain = "sandbox78984fc611a942b7ba8312015bc82e93.mailgun.org";
    $mailgunApiKey = "**************************************************";
    
    echo "<h2>Mailgun Configuration Test</h2>";
    echo "<p><strong>Domain:</strong> $mailgunDomain</p>";
    echo "<p><strong>API Key:</strong> " . substr($mailgunApiKey, 0, 10) . "..." . substr($mailgunApiKey, -10) . "</p>";
    
    // Test API connectivity
    $url = "https://api.mailgun.net/v3/$mailgunDomain/messages";
    
    // Test data
    $testData = [
        'from' => 'Test <test@' . $mailgunDomain . '>',
        'to' => '<EMAIL>',
        'subject' => 'Mailgun Test - ' . date('Y-m-d H:i:s'),
        'text' => 'This is a test email from the Mailgun configuration test script.',
        'html' => '<h1>Mailgun Test</h1><p>This is a test email from the Mailgun configuration test script.</p><p>Sent at: ' . date('Y-m-d H:i:s') . '</p>'
    ];
    
    // Initialize cURL
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
    curl_setopt($ch, CURLOPT_USERPWD, "api:$mailgunApiKey");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $testData);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    // Execute the request
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);
    
    echo "<h3>Test Results</h3>";
    echo "<p><strong>HTTP Code:</strong> $httpCode</p>";
    
    if ($curlError) {
        echo "<p style='color: red;'><strong>cURL Error:</strong> $curlError</p>";
    }
    
    if ($httpCode == 200) {
        echo "<p style='color: green;'><strong>✓ SUCCESS:</strong> Test email sent successfully!</p>";
        echo "<p><strong>Response:</strong> $response</p>";
    } else {
        echo "<p style='color: red;'><strong>✗ FAILED:</strong> HTTP $httpCode</p>";
        echo "<p><strong>Response:</strong> $response</p>";
        
        // Common error explanations
        if ($httpCode == 401) {
            echo "<p><strong>Issue:</strong> Authentication failed. Check your API key.</p>";
        } elseif ($httpCode == 400) {
            echo "<p><strong>Issue:</strong> Bad request. Check your domain and email format.</p>";
        } elseif ($httpCode == 402) {
            echo "<p><strong>Issue:</strong> Payment required. Your Mailgun account may need billing setup.</p>";
        }
    }
    
    return $httpCode == 200;
}

// Check if this is a test request
if (isset($_GET['test']) && $_GET['test'] === 'mailgun') {
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Mailgun Test - McKiness Application</title>
        <style>
            body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; line-height: 1.6; }
            .button { display: inline-block; background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 10px 0; }
            .button:hover { background: #005a87; }
            .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
            .info { background: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 5px; margin: 20px 0; }
            .success { background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px 0; }
            .error { background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 20px 0; }
        </style>
    </head>
    <body>
        <h1>Mailgun Configuration Test</h1>
        
        <div class="warning">
            <strong>Note:</strong> This test will send an actual email. Remove this file from production servers.
        </div>
        
        <?php
        if (isset($_GET['send']) && $_GET['send'] === 'true') {
            echo "<h2>Running Mailgun Test...</h2>";
            $success = testMailgunConfig();
            
            if ($success) {
                echo "<div class='success'><strong>Test completed successfully!</strong> Check your email inbox.</div>";
            } else {
                echo "<div class='error'><strong>Test failed.</strong> See details above.</div>";
            }
            
            // Show recent logs
            if (file_exists('application_logs.log')) {
                echo "<h3>Recent Log Entries:</h3>";
                $logContents = file_get_contents('application_logs.log');
                $logLines = explode("\n", $logContents);
                $recentLines = array_slice($logLines, -10);
                echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
                echo htmlspecialchars(implode("\n", $recentLines));
                echo "</pre>";
            }
        } else {
            echo "<p><a href='?test=mailgun&send=true' class='button'>Send Test Email</a></p>";
        }
        ?>
        
        <h2>Configuration Check</h2>
        <ul>
            <li><strong>Mailgun Handler:</strong> <?php echo file_exists('mailgun-handler.php') ? 'Found' : 'Missing'; ?></li>
            <li><strong>cURL Extension:</strong> <?php echo function_exists('curl_init') ? 'Available' : 'Missing'; ?></li>
            <li><strong>Form Action:</strong> Check that application.html points to mailgun-handler.php</li>
        </ul>
        
        <h2>Troubleshooting</h2>
        <ul>
            <li><strong>401 Unauthorized:</strong> Check your Mailgun API key</li>
            <li><strong>400 Bad Request:</strong> Verify your domain name and email format</li>
            <li><strong>402 Payment Required:</strong> Add billing information to your Mailgun account</li>
            <li><strong>Sandbox Domain:</strong> You're using a sandbox domain - only authorized recipients can receive emails</li>
        </ul>
        
        <h2>Next Steps</h2>
        <ol>
            <li>If test fails, check your Mailgun API credentials</li>
            <li>For production, set up a custom domain in Mailgun</li>
            <li>Add office@mckiness.<NAME_EMAIL> as authorized recipients in Mailgun</li>
            <li>Test the actual application form at <a href="application.html">application.html</a></li>
        </ol>
        
    </body>
    </html>
    <?php
    exit;
}

// Simple form to trigger test
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mailgun Test</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; text-align: center; }
        .button { display: inline-block; background: #007cba; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; }
        .button:hover { background: #005a87; }
    </style>
</head>
<body>
    <h1>Mailgun Configuration Test</h1>
    <p>Click the button below to test your Mailgun email configuration.</p>
    <a href="?test=mailgun" class="button">Run Mailgun Test</a>
</body>
</html>
