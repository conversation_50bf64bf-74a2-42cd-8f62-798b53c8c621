<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="description" content="McKiness Excavating Employment Application Form" />
  <title>McKiness Excavating Employment Application</title>
  <!-- Favicons -->
  <link rel="apple-touch-icon" sizes="180x180" href="/images/favicons/apple-touch-icon.png">
  <link rel="icon" type="image/png" sizes="32x32" href="/images/favicons/favicon-32x32.png">
  <link rel="icon" type="image/png" sizes="16x16" href="/images/favicons/favicon-16x16.png">
  <link rel="manifest" href="/images/favicons/site.webmanifest">
  <style>
    :root {
      --primary-color: #2f6156; /* McKiness green */
      --secondary-color: #f9d016; /* McKiness yellow */
      --primary-hover: #254a42;
      --secondary-hover: #e0bc14;
      --bg-light: #f9f9f9;
      --text-dark: #333;
      --border-light: #ddd;
      --section-spacing: 3rem;
      --field-spacing: 1.5rem;
    }

    * {
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      padding: 2rem;
      background-color: var(--bg-light);
      color: var(--text-dark);
      line-height: 1.6;
    }

    form {
      background-color: #fff;
      padding: 2.5rem;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      border-radius: 12px;
      max-width: 1000px;
      margin: auto;
    }

    h1, h2 {
      text-align: center;
      color: var(--primary-color);
    }

    h1 {
      margin-bottom: 1rem;
      border-bottom: 3px solid var(--secondary-color);
      padding-bottom: 1rem;
    }

    h2 {
      border-bottom: 2px solid var(--secondary-color);
      padding-bottom: 0.5rem;
      margin-top: var(--section-spacing);
      margin-bottom: 1.5rem;
    }

    .form-intro {
      text-align: center;
      margin-bottom: var(--section-spacing);
    }

    .form-section {
      margin-bottom: var(--section-spacing);
      padding-bottom: 1rem;
      border-bottom: 1px solid var(--border-light);
    }

    .form-row {
      display: flex;
      flex-wrap: wrap;
      margin: 0 -1rem;
    }

    .form-group {
      flex: 1 0 100%;
      padding: 0 1rem;
      margin-bottom: var(--field-spacing);
    }

    @media (min-width: 768px) {
      .form-group.half {
        flex: 0 0 50%;
      }
      
      .form-group.third {
        flex: 0 0 33.333%;
      }
      
      .form-group.quarter {
        flex: 0 0 25%;
      }
    }

    label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 600;
    }

    label.required::after {
      content: " *";
      color: #dc3545;
    }

    input, textarea, select {
      width: 100%;
      padding: 0.8rem;
      border-radius: 6px;
      border: 1px solid var(--border-light);
      font-size: 1rem;
      transition: border-color 0.3s;
    }

    input:focus, textarea:focus, select:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(47, 97, 86, 0.2);
    }

    input[required], textarea[required], select[required] {
      border-left: 3px solid var(--secondary-color);
    }

    input:invalid, textarea:invalid, select:invalid {
      border-color: #dc3545;
    }

    input:valid, textarea:valid, select:valid {
      border-color: var(--border-light);
    }

    textarea {
      resize: vertical;
      min-height: 100px;
    }

    .radio-group, .checkbox-group {
      margin-top: 0.5rem;
    }

    .radio-group label, .checkbox-group label {
      font-weight: 400;
      margin-bottom: 0.5rem;
      display: flex;
      align-items: center;
    }

    .radio-group label input, .checkbox-group label input {
      width: auto;
      margin-right: 0.5rem;
    }

    .helper-text {
      display: block;
      font-size: 0.85rem;
      color: #666;
      margin-top: 0.3rem;
    }

    button {
      display: block;
      width: 100%;
      margin-top: 2rem;
      padding: 1rem;
      font-size: 1.2rem;
      background-color: var(--primary-color);
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      transition: background-color 0.3s ease;
    }

    button:hover {
      background-color: var(--primary-hover);
    }

    .terms-container {
      margin-top: 2rem;
      padding: 1.5rem;
      background-color: #f8f8f8;
      border-radius: 8px;
      border-left: 4px solid var(--primary-color);
    }

    .error-message {
      background-color: #f8d7da;
      color: #721c24;
      padding: 1rem;
      border-radius: 6px;
      border: 1px solid #f5c6cb;
      margin-bottom: 2rem;
      display: none;
    }

    .error-message.show {
      display: block;
    }

    @media (max-width: 768px) {
      body {
        padding: 1rem;
      }

      form {
        padding: 1.5rem;
      }
      
      .form-group {
        flex: 0 0 100%;
      }
    }
  </style>
  <script>
    // Enhanced form validation
    function validateForm() {
      const requiredFields = [
        'email', 'first_name', 'last_name', 'start_date', 'wage',
        'high_school', 'criminal_conviction', 'employer1_name_address',
        'employer1_dates', 'employer1_position_salary', 'employer1_reason',
        'reference1', 'reference2', 'license_number', 'years_in_state'
      ];

      const requiredRadioGroups = [
        'employment_type', 'currently_employed', 'inquire_employer',
        'applied_before', 'military_service', 'national_guard',
        'iowa_license', 'cdl_license', 'on_sr22', 'past_sr22',
        'us_citizen', 'read_descriptions', 'drug_test', 'limitations',
        'willing_to_evaluate'
      ];

      let isValid = true;
      let firstErrorField = null;

      // Check required text fields
      requiredFields.forEach(fieldName => {
        const field = document.getElementById(fieldName);
        if (field && field.value.trim() === '') {
          isValid = false;
          field.style.borderColor = '#dc3545';
          if (!firstErrorField) firstErrorField = field;
        } else if (field) {
          field.style.borderColor = '';
        }
      });

      // Check required radio groups
      requiredRadioGroups.forEach(groupName => {
        const radios = document.querySelectorAll(`input[name="${groupName}"]`);
        const isChecked = Array.from(radios).some(radio => radio.checked);
        if (!isChecked) {
          isValid = false;
          radios.forEach(radio => {
            radio.parentElement.style.color = '#dc3545';
          });
          if (!firstErrorField) firstErrorField = radios[0];
        } else {
          radios.forEach(radio => {
            radio.parentElement.style.color = '';
          });
        }
      });

      // Check terms checkbox
      const termsCheckbox = document.querySelector('input[type="checkbox"][required]');
      if (termsCheckbox && !termsCheckbox.checked) {
        isValid = false;
        termsCheckbox.parentElement.style.color = '#dc3545';
        if (!firstErrorField) firstErrorField = termsCheckbox;
      } else if (termsCheckbox) {
        termsCheckbox.parentElement.style.color = '';
      }

      if (!isValid) {
        alert('Please fill in all required fields before submitting.');
        if (firstErrorField) {
          firstErrorField.focus();
          firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }

      return isValid;
    }

    // Add event listener when DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
      const form = document.querySelector('form');
      if (form) {
        form.addEventListener('submit', function(e) {
          if (!validateForm()) {
            e.preventDefault();
            return false;
          }
        });
      }

      // Check for error message in URL parameters
      const urlParams = new URLSearchParams(window.location.search);
      const errorMessage = urlParams.get('error');
      if (errorMessage) {
        const errorDiv = document.getElementById('error-message');
        if (errorDiv) {
          errorDiv.textContent = errorMessage;
          errorDiv.classList.add('show');
          errorDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }
    });
  </script>
</head>
<body>
  <form action="mailgun-handler.php" method="POST">
    <h1>McKiness Excavating Employment Application</h1>

    <div id="error-message" class="error-message"></div>

    <div class="form-intro">
      <p>WE ARE AN EQUAL OPPORTUNITY EMPLOYMENT COMPANY. WE ARE DEDICATED TO A POLICY OF NON-DISCRIMINATION IN EMPLOYMENT ON ANY BASIS INCLUDING RACE, CREED, COLOR, AGE, SEX, RELIGION, NATIONAL ORIGIN, OR PHYSICAL DEFECTS. THIS APPLICATION MUST BE FILLED OUT COMPLETELY.</p>
      <p>If you wish to submit a resume, please email it to <a href="mailto:<EMAIL>"><EMAIL></a>.  If you experience issues while submitting the application, you may also use this <a href="https://forms.gle/8L4cDcmXjsWqjqJx8" target="_blank">link</a>.</p>
    </div>
    
    <div class="form-section">
      <h2>Personal Information</h2>
      <div class="form-row">
        <div class="form-group half">
          <label class="required" for="email">Email</label>
          <input type="email" id="email" name="email" required>
        </div>
        <div class="form-group half">
          <label for="phone">Phone Number</label>
          <input type="tel" id="phone" name="phone">
        </div>
      </div>
      
      <div class="form-row">
        <div class="form-group third">
          <label class="required" for="first_name">First Name</label>
          <input type="text" id="first_name" name="first_name" required>
        </div>
        <div class="form-group third">
          <label for="middle_name">Middle Name</label>
          <input type="text" id="middle_name" name="middle_name">
        </div>
        <div class="form-group third">
          <label class="required" for="last_name">Last Name</label>
          <input type="text" id="last_name" name="last_name" required>
        </div>
      </div>
      
      <div class="form-row">
        <div class="form-group">
          <label for="address_street">Present Address - Street</label>
          <input type="text" id="address_street" name="address_street">
        </div>
      </div>
      
      <div class="form-row">
        <div class="form-group half">
          <label for="address_city">City</label>
          <input type="text" id="address_city" name="address_city">
        </div>
        <div class="form-group quarter">
          <label for="address_state">State</label>
          <input type="text" id="address_state" name="address_state">
        </div>
        <div class="form-group quarter">
          <label for="address_zip">Zip</label>
          <input type="text" id="address_zip" name="address_zip">
        </div>
      </div>
      
      <div class="form-row">
        <div class="form-group">
          <label for="referred_by">Referred By</label>
          <input type="text" id="referred_by" name="referred_by">
        </div>
      </div>
    </div>
    
    <h2>Employment Desired</h2>
    <label class="required" for="employment_type">Employment Type</label>
    <div class="radio-group">
      <label><input type="radio" id="employment_type_full" name="employment_type" value="Full Time" required> Full Time</label>
      <label><input type="radio" id="employment_type_part" name="employment_type" value="Part Time" required> Part Time</label>
      <label><input type="radio" id="employment_type_summer" name="employment_type" value="Full Time (summer only)" required> Full Time (summer only)</label>
    </div>

    <label for="position">Position</label>
    <input type="text" id="position" name="position">

    <label class="required" for="start_date">Desired Start Date</label>
    <input type="date" id="start_date" name="start_date" required>

    <label class="required" for="wage">Desired Hourly Wage</label>
    <input type="text" id="wage" name="wage" required>

    <label class="required" for="currently_employed">Currently Employed?</label>
    <div class="radio-group">
      <label><input type="radio" id="currently_employed_yes" name="currently_employed" value="Yes" required> Yes</label>
      <label><input type="radio" id="currently_employed_no" name="currently_employed" value="No" required> No</label>
    </div>

    <label class="required" for="inquire_employer">If yes, may we inquire with your present employer?</label>
    <div class="radio-group">
      <label><input type="radio" id="inquire_employer_yes" name="inquire_employer" value="Yes" required> Yes</label>
      <label><input type="radio" id="inquire_employer_no" name="inquire_employer" value="No" required> No</label>
    </div>

    <label class="required" for="applied_before">Have you previously applied to this company before?</label>
    <div class="radio-group">
      <label><input type="radio" id="applied_before_yes" name="applied_before" value="Yes" required> Yes</label>
      <label><input type="radio" id="applied_before_no" name="applied_before" value="No" required> No</label>
    </div>

    <label for="applied_when">If yes, when?</label>
    <input type="date" id="applied_when" name="applied_when">

    <h2>Education</h2>
    <label class="required" for="high_school">High School (Name, Location, Year Graduated, Relevant Subjects)</label>
    <textarea id="high_school" name="high_school" required></textarea>

    <label for="college">College (Name, Location, Year Graduated, Relevant Subjects)</label>
    <textarea id="college" name="college"></textarea>

    <label for="trade_school">Trade/Business School (Name, Location, Year Graduated, Relevant Subjects)</label>
    <textarea id="trade_school" name="trade_school" ></textarea>

    <label for="special_study">Subjects of Special Study or Research Work</label>
    <textarea id="special_study" name="special_study" ></textarea>

    <label class="required" for="military_service">U.S. Military or Naval Service</label>
    <div class="radio-group">
      <label><input type="radio" id="military_service_yes" name="military_service" value="Yes" required> Yes</label>
      <label><input type="radio" id="military_service_no" name="military_service" value="No" required> No</label>
    </div>

    <label class="required" for="national_guard">Present Membership in National Guard or Reserve</label>
    <div class="radio-group">
      <label><input type="radio" id="national_guard_yes" name="national_guard" value="Yes" required> Yes</label>
      <label><input type="radio" id="national_guard_no" name="national_guard" value="No" required> No</label>
    </div>

    <label for="activities">Activities other than religious, civic, athletic, fraternal, etc</label>
    <sub>Do not include organizations, the name or character of which indicates the race, creed, color or national origin of its members.</sub>
    <textarea id="activities" name="activities"></textarea>

    <label class="required" for="criminal_conviction">Have you been convicted of a crime in the past ten years excluding misdemeanors and summary offenses, which has not been annulled, expunged or sealed by a court? If yes, please describe in full.</label>
    <textarea id="criminal_conviction" name="criminal_conviction" required></textarea>


    <h2>Former Employers</h2>
    <sub>List up to four previous employers starting with the most current.</sub>
    <label class="required" for="employer1_name_address">Employer #1 Name and Address</label>
    <textarea id="employer1_name_address" name="employer1_name_address" required></textarea>
    <label class="required" for="employer1_dates">Date Range Employed</label>
    <input type="text" id="employer1_dates" name="employer1_dates" required>
    <label class="required" for="employer1_position_salary">Position and Salary</label>
    <textarea id="employer1_position_salary" name="employer1_position_salary" required></textarea>
    <label class="required" for="employer1_reason">Reason for Leaving</label>
    <textarea id="employer1_reason" name="employer1_reason" required></textarea>

    <label for="employer2_name_address">Employer #2 Name and Address</label>
    <textarea id="employer2_name_address" name="employer2_name_address"></textarea>
    <label for="employer2_dates">Date Range Employed</label>
    <input type="text" id="employer2_dates" name="employer2_dates">
    <label for="employer2_position_salary">Position and Salary</label>
    <textarea id="employer2_position_salary" name="employer2_position_salary"></textarea>
    <label for="employer2_reason">Reason for Leaving</label>
    <textarea id="employer2_reason" name="employer2_reason"></textarea>

    <label for="employer3_name_address">Employer #3 Name and Address</label>
    <textarea id="employer3_name_address" name="employer3_name_address"></textarea>
    <label for="employer3_dates">Date Range Employed</label>
    <input type="text" id="employer3_dates" name="employer3_dates">
    <label for="employer3_position_salary">Position and Salary</label>
    <textarea id="employer3_position_salary" name="employer3_position_salary"></textarea>
    <label for="employer3_reason">Reason for Leaving</label>
    <textarea id="employer3_reason" name="employer3_reason"></textarea>

    <label for="employer4_name_address">Employer #4 Name and Address</label>
    <textarea id="employer4_name_address" name="employer4_name_address"></textarea>
    <label for="employer4_dates">Date Range Employed</label>
    <input type="text" id="employer4_dates" name="employer4_dates">
    <label for="employer4_position_salary">Position and Salary</label>
    <textarea id="employer4_position_salary" name="employer4_position_salary"></textarea>
    <label for="employer4_reason">Reason for Leaving</label>
    <textarea id="employer4_reason" name="employer4_reason"></textarea>

    <h2>References</h2>
    <sub>Give the names of two persons not related to you whom you have known at least one year. Should be connected to work or knows of your work.</sub>
    <label class="required" for="reference1">Reference #1 (Name, Phone, City, State, Business, Years Acquainted)</label>
    <textarea id="reference1" name="reference1" required></textarea>
    <label class="required" for="reference2">Reference #2 (Name, Phone, City, State, Business, Years Acquainted)</label>
    <textarea id="reference2" name="reference2" required></textarea>

    <h2>Experience</h2>
    <sub>No one will be considered who does not show experience/qualifications completely.  Show machines operated, labor performed, trucks driven, number of years, and for whom.</sub>
    <label for="carpenter_experience">Carpenters, Cement Mason, Steel Worker (Types of construction, years, and companies)</label>
    <textarea id="carpenter_experience" name="carpenter_experience"></textarea>
    <label for="laborer_experience">Laborer (Tasks performed, years, and companies)</label>
    <textarea id="laborer_experience" name="laborer_experience"></textarea>
    <label for="operator_experience">Operators (Machines operated, years, and companies)</label>
    <textarea id="operator_experience" name="operator_experience"></textarea>
    <label for="truck_driver_experience">Truck Drivers (Trucks driven, years, and companies)</label>
    <textarea id="truck_driver_experience" name="truck_driver_experience"></textarea>

    <h2>Driver Information</h2>
    <label class="required" for="iowa_license">Do you have a state of Iowa driver's license?</label>
    <div class="radio-group">
      <label><input type="radio" id="iowa_license_yes" name="iowa_license" value="Yes" required> Yes</label>
      <label><input type="radio" id="iowa_license_no" name="iowa_license" value="No" required> No</label>
    </div>
    <label class="required" for="license_number">Driver's License Number</label>
    <input type="text" id="license_number" name="license_number" required>
    <label class="required" for="cdl_license">Do you have a CDL license?</label>
    <div class="radio-group">
      <label><input type="radio" id="cdl_license_yes" name="cdl_license" value="Yes" required> Yes</label>
      <label><input type="radio" id="cdl_license_no" name="cdl_license" value="No" required> No</label>
    </div>
    <label for="cdl_class">If yes, what class?</label>
    <sub>CLASS A: Vehicle Gross Weight of 26,001 lbs. or more and towed unit is 10001 lbs or more.    CLASS B: Vehicle Gross Weight of 26,001 lbs. or more and towed unit is less than 10001 lbs.      CLASS C: Vehicle Gross Weight is less than 26,001 lbs.</sub>
    <input type="text" id="cdl_class" name="cdl_class">
    <label class="required" for="on_sr22">Are you on SR-22 insurance?</label>
    <div class="radio-group">
      <label><input type="radio" id="on_sr22_yes" name="on_sr22" value="Yes" required> Yes</label>
      <label><input type="radio" id="on_sr22_no" name="on_sr22" value="No" required> No</label>
    </div>
    <label class="required" for="past_sr22">Have you ever been on SR-22?</label>
    <div class="radio-group">
      <label><input type="radio" id="past_sr22_yes" name="past_sr22" value="Yes" required> Yes</label>
      <label><input type="radio" id="past_sr22_no" name="past_sr22" value="No" required> No</label>
    </div>
    <label for="sr22_when">If yes, when?</label>
    <input type="text" id="sr22_when" name="sr22_when">

    <h2>Additional</h2>
    <label for="supervisor_experience">Job Foreman or Superintendent (Project types/sizes, people managed, years, companies)</label>
    <sub>Please include types and sizes of projects, number of people under your supervision, number of years, and company names.</sub>
    <textarea id="supervisor_experience" name="supervisor_experience"></textarea>
    <label for="other_responsibilities">Other job-related responsibilities experienced in the past</label>
    <textarea id="other_responsibilities" name="other_responsibilities"></textarea>
    <label class="required" for="us_citizen">Are you a U.S. citizen?</label>
    <div class="radio-group">
      <label><input type="radio" id="us_citizen_yes" name="us_citizen" value="Yes" required> Yes</label>
      <label><input type="radio" id="us_citizen_no" name="us_citizen" value="No" required> No</label>
    </div>
    <label class="required" for="years_in_state">Years in State</label>
    <input type="text" id="years_in_state" name="years_in_state" required>
    <label class="required" for="read_descriptions">Have you read the job descriptions? <a href="https://cutt.ly/SIGA1Cu">Job Descriptions</a></label>
    <div class="radio-group">
      <label><input type="radio" id="read_descriptions_yes" name="read_descriptions" value="Yes" required> Yes</label>
      <label><input type="radio" id="read_descriptions_no" name="read_descriptions" value="No" required> No</label>
    </div>

    <label class="required" for="drug_test">Are you willing to take a drug test?</label>
    <div class="radio-group">
      <label><input type="radio" id="drug_test_yes" name="drug_test" value="Yes" required> Yes</label>
      <label><input type="radio" id="drug_test_no" name="drug_test" value="No" required> No</label>
    </div>

    <label class="required" for="limitations">Is there anything physically or mentally that would prevent you from performing the described tasks? <a href="https://cutt.ly/SIGA1Cu">Job Descriptions</a></label>
    <div class="radio-group">
      <label><input type="radio" id="limitations_yes" name="limitations" value="Yes" required> Yes</label>
      <label><input type="radio" id="limitations_no" name="limitations" value="No" required> No</label>
    </div>
    <label for="limitations_explanation">If yes, please explain</label>
    <textarea id="limitations_explanation" name="limitations_explanation"></textarea>
    <label class="required" for="willing_to_evaluate">If no, are you willing to take a functional capacity evaluation and/or fitness for duty exam?</label>
    <div class="radio-group">
      <label><input type="radio" id="willing_to_evaluate_yes" name="willing_to_evaluate" value="Yes" required> Yes</label>
      <label><input type="radio" id="willing_to_evaluate_no" name="willing_to_evaluate" value="No" required> No</label>
    </div>

    <p>APPLICANT PLEASE READ CAREFULLY

	PLEASE ANSWER ALL THE QUESTIONS ON THIS FORM TO THE BEST OF YOUR ABILITY. YOUR QUALIFICATIONS WILL BE CAREFULLY REVIEWED AND YOU WILL BE GIVEN THOROUGH CONSIDERATION FOR ANY SUITABLE VACANCIES IN THE COMPANY. IF YOU ARE EMPLOYED, THIS WILL BECOME PART OF YOUR PERMANENT PERSONNEL RECORD. WE APPRECIATE YOUR INTEREST AS SHOWN BY YOUR FILLING OUT THIS APPLICATION FORM.

	I HEREBY AFFIRM AND DECLARE THAT ALL THE FOREGOING STATEMENTS ARE TRUE AND CORRECT, AND THAT I HAVE NOT KNOWINGLY WITHHELD ANY FACT THAT WOULD, IF DISCLOSED, AFFECT MY APPLICATION UNFAVORABLY. I HEREBY AUTHORIZE THE COMPANY TO CONDUCT ANY INVESTIGATION TOGETHER WITH THEIR OPINIONS ON THESE MATTERS WITHOUT ANY LIABILITY FOR ANY DAMAGE WHATSOEVER CAUSED EITHER DIRECTLY OR INDIRECTLY BY GIVING OR RECEIVING THIS INFORMATION OR OPINIONS. THE INVESTIGATIONS WILL INCLUDE BUT ARE NOT LIMITED TO RUNNING MOTOR VEHICLE REPORTS ON THE APPLICANT TO DETERMINE THEIR DRIVING RECORD AND DRIVING INSURABILITY. I AUTHORIZE MY FORMER AND PRESENT EMPLOYERS AND PERSONAL REFERENCES TO GIVE ANY INFORMATION THEY MAY HAVE CONCERNING MY CHARACTER, HEALTH AND EMPLOYMENT RECORD. IT IS UNDERSTOOD THAT A FALSE STATEMENT ON THIS APPLICATION MAY BE CONSIDERED A SUFFICIENT CAUSE FOR REJECTION OF THIS APPLICATION, OR DISMISSAL, IF ALREADY EMPLOYED BY THE COMPANY.

	IN OUR ONGOING EFFORT TO MAINTAIN A DRUG FREE WORK-PLACE, WE REQUIRE A VALID, NEGATIVE DRUG TEST AS A CONDITION OF EMPLOYMENT. FOR THIS TEST, YOU WILL BE ASKED TO PRODUCE AT LEAST 1/4 CUP OF URINE. PLEASE REMEMBER NOT TO URINATE WITHIN 1 HOUR BEFORE YOUR TEST AND DRINK NO MORE THAN 2 TO 3 CUPS OF LIQUID BEFORE THE TEST. ALSO, YOU WILL BE REQUIRED TO SUPPLY THE COLLECTION SITE WITH A PICTURE ID. ANY ATTEMPT TO DILUTE, SUBSTITUTE, OR CONTAMINATE YOUR SPECIMEN WILL RESULT IN AN INVALID TEST. THIS WILL PREVENT EMPLOYMENT WITH THIS COMPANY.

	BY SUBMITTING BELOW YOU AGREE TO PARTAKE IN PRE-EMPLOYMENT DRUG TESTING AND YOU AGREE TO ALLOW THE COMPANY TO RUN AND REVIEW THE APPROPRIATE MOTOR VEHICLE REPORTS. ANY OFFER OF EMPLOYMENT WILL BE CONTINGENT ON PASSING THE PRE-EMPLOYMENT DRUG TEST AND HAVING A SATISFACTORY MOTOR VEHICLE REPORT. YOU ALSO AGREE TO A PRE-EMPLOYMENT FUNCTIONAL CAPACITY EVALUATION AND OR FITNESS FOR DUTY EXAM.

If you wish to submit a resume, please email it to <a href="mailto:<EMAIL>"><EMAIL></a></p>

    <label>
      <input type="checkbox" required /> I have read and agree to the terms above
    </label>

    <button type="submit">Submit Application</button>

    <input type="hidden" name="_captcha" value="false">
    <input type="hidden" name="_next" value="https://mckiness.com/thankyou.html">

  </form>
</body>
</html>
