/*
 * jQuery FlexSlider v2.0
 * http://www.woothemes.com/flexslider/
 *
 * Copyright 2012 WooThemes
 * Free to use under the GPLv2 license.
 * http://www.gnu.org/licenses/gpl-2.0.html
 *
 * Contributing author: <PERSON> (@mbmufffin)
 */

/* Browser Resets */

.flex-container a:active,
.flexslider a:active,
.flex-container a:focus,
.flexslider a:focus {
	outline: none;
}

.slides,
.flex-control-nav,
.flex-direction-nav {
	margin: 0;
	padding: 0;
	list-style: none;
}

/* FlexSlider Necessary Styles
*********************************/

.flexslider .slides>li {
	display: none;
	-webkit-backface-visibility: hidden;
}

/* Hide the slides before the JS is loaded. Avoids image jumping */

.flexslider .slides img {
	display: block;
}

.flex-pauseplay span {
	text-transform: capitalize;
}

/* Clearfix for the .slides element */

.slides:after {
	content: ".";
	display: block;
	clear: both;
	visibility: hidden;
	line-height: 0;
	height: 0;
}

html[xmlns] .slides {
	display: block;
}

* html .slides {
	height: 1%;
}

/* No JavaScript Fallback */

/* If you are not using another script, such as Modernizr, make sure you
 * include js that eliminates this class on page load */

.no-js .slides>li:first-child {
	display: block;
}

/* FlexSlider Default Theme
*********************************/

.flexslider {
	border: 0px;
	position: relative;
	zoom: 1;
}

.flex-viewport {
	max-height: 2000px;
	-webkit-transition: all 1s ease;
	-moz-transition: all 1s ease;
	transition: all 1s ease;
}

.loading .flex-viewport {
	max-height: 300px;
}

.flexslider .slides {
	zoom: 1;
}

.carousel li {
	margin-right: 5px
}

/* Direction Nav */

.flex-direction-nav {
	*height: 0;
}

.flex-direction-nav a {
	width: 45px;
	height: 48px;
	border: 1px solid #af8c22;
	box-shadow: 3px 3px 2px -1px rgba(0, 0, 0, 0.35);
	display: block;
	background: #f1cf69 url(../images/right.png) no-repeat 7px 11px;
	position: absolute;
	bottom: 8%;
	z-index: 10;
	cursor: pointer;
	text-indent: -9999px;
	opacity: 1;
	border-radius: 20px 0 0 0;
	-webkit-transition: all .3s ease;
}

.flex-direction-nav .flex-next {
	background: #f1cf69 url(../images/left.png) no-repeat 4px 11px;
	right: 44%;
	border-radius: 0 20px 0 0;
}

.flex-direction-nav .flex-prev {
	left: 58%;
}

/*
.flexslider:hover .flex-next {opacity: 1;}
.flexslider:hover .flex-prev {opacity: 1;}
.flexslider:hover .flex-next:hover, .flexslider:hover .flex-prev:hover {opacity: 1;}
.flex-direction-nav .flex-disabled {opacity: .3!important; filter:alpha(opacity=30); cursor: default;}*/

.flex-next:hover,
.flex-prev:hover {
	opacity: .8;
}

/* Control Nav */

.flex-control-nav {
	display: block;
	position: absolute;
	left: 9px;
	margin-left: 0;
	bottom: 20%;
}

.flex-control-nav li {
	margin: 0 .5em;
	display: inline-block;
	zoom: 1;
	position: relative;
}

.flex-control-paging li a {
	width: 10px;
	height: 10px;
	display: block;
	background: none;
	cursor: pointer;
	text-indent: -9999px;
	border: none;
	color: #fff;
	background: #000;
	text-align: center;
	border-radius: 15px;
	font-weight: 600;
	display: none;
}

.flex-control-paging li a.flex-active {
	background: #3be8b0;
	cursor: default;
	transform: rotateX(360deg);
	-webkit-transform: rotateX(360deg);
	-moz-transform: rotateX(360deg);
	-o-transform: rotateX(360deg);
	-ms-transform: rotateX(360deg);
	transition: transform 2s;
}

.flex-control-thumbs {
	margin: 5px 0 0;
	position: static;
	overflow: hidden;
}

.flex-control-thumbs li {
	width: 25%;
	float: left;
	margin: 0;
}

.flex-control-thumbs img {
	width: 100%;
	display: block;
	opacity: .7;
	cursor: pointer;
}

.flex-control-thumbs img:hover {
	opacity: 1;
}

.flex-control-thumbs .flex-active {
	opacity: 1;
	cursor: default;
}

@media screen and (max-width:1080px) {
	.flex-direction-nav .flex-prev {
		left: 56%;
	}
	.flex-direction-nav .flex-next {
		right: 44%;
	}
	.flex-direction-nav a {
		bottom: 7%;
	}
}

@media screen and (max-width: 991px) {
	.flex-direction-nav .flex-next {
		right: 41%;
	}
	.flex-direction-nav .flex-prev {
		left: 59%;
	}
	.flex-direction-nav a {
		bottom: 0%;
	}
}

@media screen and (max-width: 667px) {
	.flex-direction-nav a {
		bottom: -4%;
	}
	.flex-direction-nav .flex-next {
		background-position: 4px;
	}
	.flex-direction-nav .flex-prev {
		background-position: 4px;
	}
	.flex-direction-nav a {
		width: 31px;
		height: 36px;
	}
}

@media screen and (max-width: 640px) {
	.flex-direction-nav .flex-prev {
		left: 37px;
	}
	.flex-direction-nav .flex-next {
		right: 93%;
	}
	.flex-direction-nav a {
		width: 35px;
		height: 44px;
		bottom: -12%;
	}
}

@media screen and (max-width: 600px) {}

@media screen and (max-width: 480px) {
	.flex-direction-nav .flex-prev {
		left: 28px;
	}
	.flex-direction-nav a {
		bottom: -15%;
	}
}

@media screen and (max-width: 414px) {
	.flex-direction-nav .flex-next {
		right: 91%;
	}
}

@media screen and (max-width: 375px) {
	.flex-direction-nav .flex-prev {
		left: 27px;
	}
}

@media screen and (max-width: 320px) {
	.flex-direction-nav .flex-next {
		right: 89%;
	}
}