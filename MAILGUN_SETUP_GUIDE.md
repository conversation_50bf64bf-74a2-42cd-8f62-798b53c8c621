# McKiness Application Mailgun Setup Guide

## Current Status
✅ **Form updated** to use `mailgun-handler.php`  
✅ **Enhanced validation** for all required fields  
✅ **Complete email template** with all form sections  
✅ **Proper recipient configuration** (<EMAIL>, <EMAIL>, BCC: <EMAIL>)  
✅ **Error handling and logging** implemented  

## Issues Fixed

### 1. Form Submission Target
- **Before:** Form was pointing to `sendemail.php` (unreliable PHP mail)
- **After:** Form now points to `mailgun-handler.php` (reliable Mailgun API)

### 2. Recipient Configuration
- **Before:** Only sending to `<EMAIL>`
- **After:** Sending to `<EMAIL>,<EMAIL>` with BCC to `<EMAIL>`

### 3. Validation Enhancement
- **Before:** Basic validation for only 3 fields
- **After:** Complete validation for all 30+ required fields

### 4. Email Content
- **Before:** Missing many form sections
- **After:** Complete email with all form data organized by sections

## Testing Your Setup

### Quick Test
1. Visit: `http://yourdomain.com/test-mailgun.php`
2. Click "Run Mailgun Test"
3. Check if test email is received

### Full Application Test
1. Visit: `http://yourdomain.com/application.html`
2. Fill out the form completely
3. Submit and check for success/error messages

## Current Mailgun Configuration

Your `mailgun-handler.php` is currently configured with:
- **Domain:** `sandbox78984fc611a942b7ba8312015bc82e93.mailgun.org`
- **API Key:** `**************************************************`

⚠️ **Important:** This is a sandbox domain with limitations.

## Sandbox Domain Limitations

With a sandbox domain, emails can only be sent to:
- **Authorized Recipients** (must be added in Mailgun dashboard)
- **Maximum 300 emails per day**
- **"via mailgun.org" in sender name**

### Adding Authorized Recipients
1. Log into your Mailgun dashboard
2. Go to Sending → Domains → [Your Sandbox Domain]
3. Click "Authorized Recipients"
4. Add these emails:
   - `<EMAIL>`
   - `<EMAIL>`
   - `<EMAIL>`

## Production Setup (Recommended)

### 1. Set Up Custom Domain
1. In Mailgun dashboard, go to Sending → Domains
2. Click "Add New Domain"
3. Enter: `mg.mckiness.com` (or similar subdomain)
4. Follow DNS setup instructions

### 2. Update DNS Records
Add these DNS records to your domain:
```
Type: TXT
Name: mg.mckiness.com
Value: v=spf1 include:mailgun.org ~all

Type: TXT  
Name: smtp._domainkey.mg.mckiness.com
Value: [Mailgun will provide this]

Type: CNAME
Name: email.mg.mckiness.com
Value: mailgun.org

Type: MX
Name: mg.mckiness.com
Value: mxa.mailgun.org (Priority: 10)
Value: mxb.mailgun.org (Priority: 10)
```

### 3. Update Configuration
In `mailgun-handler.php`, change:
```php
$mailgunDomain = "mg.mckiness.com"; // Your custom domain
$mailgunApiKey = "your-production-api-key"; // Your production API key
```

## Troubleshooting Common Issues

### 1. "401 Unauthorized"
- **Cause:** Invalid API key
- **Solution:** Check your API key in Mailgun dashboard

### 2. "400 Bad Request"
- **Cause:** Invalid domain or email format
- **Solution:** Verify domain name and email addresses

### 3. "402 Payment Required"
- **Cause:** Account needs billing setup
- **Solution:** Add payment method in Mailgun dashboard

### 4. "Emails not received"
- **Cause:** Using sandbox domain without authorized recipients
- **Solution:** Add recipients to authorized list or use custom domain

### 5. "Form submission fails"
- **Cause:** Missing required fields or validation errors
- **Solution:** Check `application_logs.log` for specific errors

## Monitoring and Logs

### Check Logs
- **Application logs:** `application_logs.log`
- **Form submissions:** `form_submissions/` directory
- **Mailgun dashboard:** View delivery statistics

### Log Files Location
```
application_logs.log          # Error and success logs
form_submissions/            # Backup of all form submissions
  └── 2024-01-15_14-30-25_Smith.json
```

## Security Considerations

### Production Checklist
- [ ] Use custom domain instead of sandbox
- [ ] Store API keys in environment variables
- [ ] Remove test files (`test-mailgun.php`)
- [ ] Set up proper DNS records
- [ ] Enable HTTPS for form submission
- [ ] Monitor delivery rates

### Environment Variables (Recommended)
Instead of hardcoding in `mailgun-handler.php`:
```php
$mailgunDomain = $_ENV['MAILGUN_DOMAIN'] ?? 'mg.mckiness.com';
$mailgunApiKey = $_ENV['MAILGUN_API_KEY'] ?? 'your-api-key';
```

## Support Resources

### Mailgun Documentation
- [Getting Started](https://documentation.mailgun.com/en/latest/quickstart.html)
- [API Reference](https://documentation.mailgun.com/en/latest/api_reference.html)
- [Domain Setup](https://documentation.mailgun.com/en/latest/user_manual.html#domains)

### Contact Information
- **Mailgun Support:** <EMAIL>
- **DNS Help:** Contact your domain registrar
- **Server Issues:** Contact your hosting provider

## Quick Commands

### Test Mailgun API (Command Line)
```bash
curl -s --user 'api:YOUR_API_KEY' \
    https://api.mailgun.net/v3/YOUR_DOMAIN/messages \
    -F from='Test <test@YOUR_DOMAIN>' \
    -F to='<EMAIL>' \
    -F subject='Test Email' \
    -F text='This is a test email'
```

### Check DNS Records
```bash
nslookup -type=TXT mg.mckiness.com
nslookup -type=MX mg.mckiness.com
```

---

**Next Steps:**
1. Test current setup with `test-mailgun.php`
2. Add authorized recipients for sandbox domain
3. Plan migration to custom domain for production
4. Monitor logs and delivery rates
