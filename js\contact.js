//send us a message form js
                 $(function () {
    $(".customform").submit(function(event){
 // variable to hold request
    var request;
    // bind to the submit event of our form
    $("#result").html('');

    // abort any pending request
    if (request) {
        request.abort();
    }
    // setup some local variables
    var $form = $(this);
    // let's select and cache all the fields
    var $inputs = $form.find("input, select, button, textarea");
    // serialize the data in the form
    var serializedData = $form.serialize();

    // let's disable the inputs for the duration of the ajax request
    $inputs.prop("disabled", true);

    // fire off the request to /form.php
    request = $.ajax({
        url: "ContactHandler.php",
        type: "post",
        data: serializedData
    });

    // callback handler that will be called on success
    request.done(function (response, textStatus, jqXHR){
        // log a message to the console
        console.log("Response: "+response);
        obj = JSON.parse(response);
        if (obj.success == true)
{
    alert( obj.message);
        console.log("Response:success detected ");
$("#name").val("");
$("#email").val("");
$("#comments").val("");
$("#phone").val("");

}

else{
    $(".sendB").notify(obj.message, {position:"right"});
               // $("#result").html('Failed:' +obj.message);

}
    });

    // callback handler that will be called on failure
    request.fail(function (jqXHR, textStatus, errorThrown){
        // log the error to the console
        console.error(
            "The following error occured: "+
            textStatus, errorThrown
        );
        alert("Something bad happened.. I mean error code..uh.. 43");

    });

    // callback handler that will be called regardless
    // if the request failed or succeeded
    request.always(function () {
        // reenable the inputs
        $inputs.prop("disabled", false);
    });

    // prevent default posting of form
    event.preventDefault();
});
});
