ul.navbar-nav {
    background:#f9d016;
}

.navbar-light .navbar-nav .nav-link {
    font-size:25px;
}

.header-contact-w3ls a{
    background:#2f6156;
}

.header-contact-w3ls a:hover {
    background:#f9d016;
}

h3.title span {
    color:#2f6156;
}

.carousel-indicators .active {
    background-color:#f9d016!important;
}

.navbar-brand {
    padding-top:0;
    padding-bottom:0;
}

.banner-agile-text h4 {
    text-shadow: 2px 2px black;
    font-weight:1000;
}

.agileits_works-grid p {
  font-size:18px;
  color:#333;
}

.works-grid p {
  font-size:18px;
  color:#333;
}

.breadcrumb {
  font-size:25px;
}

.breadcrumb li a {
  color:#2f6156;
}

.about-grids {
  border-left: 5px solid #2f6156;
  flex-basis: none;
  -webkit-flex-basis: 100%;
  }

.blogs {
  margin-bottom:10em;
  }

  .contact_wthreerow h4 {
    color:#2f6156;
  }

  ul.contact_info li a:hover {
    color:#2f6156;
  }

.w3l_contact_form input[type="submit"] {
    background:#2f6156;
}

.w3l_contact_form input[type="submit"]:hover {
    background:#f9d016;
}

ul.contact_info li i {
  color:#f9d016;
}

.ftr-menu, .subscribe {
  background:#f9d016;
}

button.navbar-toggler {
  background:#f9d016;
}

.dropdown-item {
    font-size: 23px;
    margin-bottom: 15px;
    margin-top: 15px;
  }

.dropdown-item:hover, .dropdown-item:focus {
  background-color: #9d9ea0;
}

.comments-grid-right p {
  font-size:18px;
  color:#555;
}

.comments-grid-left h3{
  color:#2f6156;
  font-size:2.75em;
  margin-bottom:10px;
}

.about-grids h3 {
  color:#2f6156;
  font-size:2.75em;
  margin-bottom:10px;
}

.about-grids2 h3 {
  color:#2f6156;
  font-size:2.75em;
  margin-bottom:10px;
}

.about-grids2 {
    border-right: 3px solid #f9d016;
    flex-basis: none;
    -webkit-flex-basis: 100%;
}

.about-grids2 h5 {
    font-size: 20px;
    letter-spacing: 1px;
}

.ser h3{
  float: right;
}

.ser img {
  float: right;
}

p.paragraphf {
  font-size:16px;
  color:#555;
}

.service p {
  font-size:20px;
  color:#555;
}

@media(max-width: 480px) {
.banner-agile-text h4 {
  text-shadow:none;
  background:#0000006b;
  padding:5px;
}
}

@media(max-width: 600px) {
.ftr-menu ul li {padding:15px};

}

.banner-agile-text p::before {
  background: none;
  text-align: center;

}

.banner-agile-text p {
  margin-left: 0;
}

.fronttext {
  background:#0000006b;
}

@media(min-width: 991px){
    .apply{
        display:none;
    }
}
